﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;

namespace Axon.Core.Authentication.Configuration;

public class ConfigureTokenValidationParameters(
    IOptionsMonitor<JwtAuthenticationOptions> jwtOptions) : IPostConfigureOptions<JwtBearerOptions>
{
    private readonly JwtAuthenticationOptions jwtAuthenticationOptions = jwtOptions.CurrentValue;
    public void PostConfigure(string? name, JwtBearerOptions options)
    {
        if (name == JwtBearerAuthenticationDefaults.AuthenticationScheme)
        {
            options.TokenValidationParameters = CreateTokenValidationParameters();
        }
    }

    private TokenValidationParameters CreateTokenValidationParameters() => new()
    {
        ValidIssuer = jwtAuthenticationOptions.Issuer,
        ValidAudience = jwtAuthenticationOptions.Audience,
        ClockSkew = jwtAuthenticationOptions.ClockSkew,
    };
}
