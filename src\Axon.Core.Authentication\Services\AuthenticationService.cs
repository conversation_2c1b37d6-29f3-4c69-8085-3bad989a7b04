using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.JsonWebTokens;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Axon.Core.Authentication.Services;

public class AuthenticationService(
    IOptionsMonitor<JwtBearerOptions> jwtBearerOptions,
    ILogger<AuthenticationService> logger) : IAuthenticationService
{
    public async Task<ClaimsPrincipal?> ValidateTokenAsync(string token)
    {
        try
        {
            var options = jwtBearerOptions.Get(JwtBearerAuthenticationDefaults.AuthenticationScheme);
            var tokenHandler = new JsonWebTokenHandler();
            var result = await tokenHandler.ValidateTokenAsync(token, options.TokenValidationParameters);

            if (!result.IsValid)
            {
                logger.LogWarning("Token validation failed: {Exception}", result.Exception?.Message);
                return null;
            }
            return new ClaimsPrincipal(result.ClaimsIdentity);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Exception occurred during token validation");
            return null;
        }
    }

    public async Task SignInUserAsync(HttpContext httpContext, ClaimsPrincipal claimsPrincipal)
    {
        await httpContext.SignInAsync(JwtBearerAuthenticationDefaults.AuthenticationScheme, claimsPrincipal);
    }
}
