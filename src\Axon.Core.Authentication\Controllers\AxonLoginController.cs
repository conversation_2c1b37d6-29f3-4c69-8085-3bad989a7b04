using System;
using System.Threading.Tasks;
using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Services;
using Axon.Core.Authentication.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Axon.Core.Authentication.Controllers;

[ApiController]
public class AxonLoginController(
    ITokenService tokenService,
    IAuthenticationService authenticationService,
    IOptionsMonitor<JwtAuthenticationOptions> options,
    ILogger<AxonLoginController> logger) : ControllerBase
{
    private readonly JwtAuthenticationOptions jwtOptions = options.CurrentValue;

    [Route("/axon-auth/sign-in")]
    [HttpGet]
    public async Task<IActionResult> Login([FromQuery] string code, [FromQuery] string redirectUrl)
    {
        try
        {
            if (string.IsNullOrEmpty(code))
            {
                logger.LogWarning("Login attempt with missing or empty code parameter");
                return BadRequest(AuthenticationConstants.ErrorMessages.CodeRequired);
            }

            if (string.IsNullOrEmpty(redirectUrl))
            {
                redirectUrl = jwtOptions.DefaultRedirectUrl;
            }

            logger.LogInformation("Processing login request with code for redirect to {RedirectUrl}", redirectUrl);

            var token = await tokenService.GetTokenAsync(code);

            if (string.IsNullOrEmpty(token))
            {
                logger.LogWarning("Failed to retrieve token from organisation API for code");
                return Unauthorized(AuthenticationConstants.ErrorMessages.TokenRetrievalFailed);
            }

            var claimsPrincipal = await authenticationService.ValidateTokenAsync(token);

            if (claimsPrincipal?.Identity?.IsAuthenticated is not true)
            {
                logger.LogWarning("Token validation failed");
                return Unauthorized(AuthenticationConstants.ErrorMessages.TokenValidationFailed);
            }

            await authenticationService.SignInUserAsync(HttpContext, claimsPrincipal);

            logger.LogInformation("User successfully authenticated and signed in, redirecting to {RedirectUrl}", redirectUrl);

            return new RedirectResult(redirectUrl);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred during login process");
            return StatusCode(AuthenticationConstants.HttpStatusCodes.InternalServerError,
                AuthenticationConstants.ErrorMessages.AuthenticationError);
        }
    }


}
