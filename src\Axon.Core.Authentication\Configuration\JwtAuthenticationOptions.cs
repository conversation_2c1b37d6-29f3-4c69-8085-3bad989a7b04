using System;

namespace Axon.Core.Authentication.Configuration;

public class JwtAuthenticationOptions
{
    public const string AxonCoreAuthenticationConfigKey = "AxonCoreAuthentication";

    public string Issuer { get; set; } = string.Empty;
    public string Audience { get; set; } = string.Empty;
    public string PublicKeyApiHost { get; set; } = string.Empty;
    public string Tenant { get; set; } = "phlexglobal";

    public TimeSpan ClockSkew { get; set; } = TimeSpan.FromMinutes(5);

    public string UserIdClaimType { get; set; } = "sub";
    public string EmailClaimType { get; set; } = "email";
    public string NameClaimType { get; set; } = "name";
    public string DefaultRedirectUrl { get; set; } = "/";
}
