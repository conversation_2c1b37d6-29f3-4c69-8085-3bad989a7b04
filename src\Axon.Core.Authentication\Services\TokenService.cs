using System;
using System.Net.Http;
using System.Threading.Tasks;
using Axon.Core.Api.Sdk.NetCore.Api;
using Axon.Core.Authentication.Configuration;
using CommunityToolkit.Diagnostics;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Axon.Core.Authentication.Services;

public class TokenService(
    HttpClient httpClient,
    IOptionsMonitor<JwtAuthenticationOptions> options,
    ILogger<TokenService> logger) : ITokenService
{
    private readonly OrganisationApi organisationApi = new(httpClient, options.CurrentValue.PublicKeyApiHost);

    public async Task<string> GetTokenAsync(string code)
    {
        Guard.IsNotNullOrEmpty(code);

        try
        {
            var response = await organisationApi.IssueTokenAsync(code);
            return response.Data.Token;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to exchange code for token");
            throw new InvalidOperationException($"Failed to exchange code for token: {ex.Message}", ex);
        }
    }
}
