﻿using Axon.Core.Api.Sdk.NetCore.Api;
using Axon.Core.Authentication.Interfaces;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

namespace Axon.Core.Authentication.Configuration;

public class JwtKeyProvider(
    IMemoryCache memoryCache,
    HttpClient httpClient,
    IOptionsMonitor<JwtAuthenticationOptions> options) : IJwtKeyProvider
{
    private readonly OrganisationApi organisationApi = new(httpClient, options.CurrentValue.PublicKeyApiHost);
    private readonly JwtAuthenticationOptions jwtAuthenticationSettings = options.CurrentValue;

    public async Task<IList<JsonWebKey>> GetKeyAsync(string kid)
    {
        return await memoryCache.GetOrCreateAsync(kid, async _ =>
        {
            var response = await organisationApi.GetKeysAsync(jwtAuthenticationSettings.Tenant);
            return response.Data.Keys;
        }) ?? [];
    }
}
