﻿using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Extensions;
using Axon.Core.Authentication.Utilities;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Axon.Core.Authentication.Events;

public class AxonJwtBearerEvents(ILogger<AxonJwtBearerEvents> logger, IOptionsMonitor<JwtAuthenticationOptions> options) : JwtBearerEvents
{
    private readonly JwtAuthenticationOptions jwtAuthenticationSettings = options.CurrentValue;

    public override async Task TokenValidated(TokenValidatedContext context)
    {
        try
        {
            var claims = context.Principal?.Claims.ToList() ?? [];
            var userId = claims.GetClaimValue(jwtAuthenticationSettings.UserIdClaimType);
            var email = claims.GetClaimValue(jwtAuthenticationSettings.EmailClaimType);

            if (string.IsNullOrWhiteSpace(userId))
            {
                context.Fail($"Missing required claim: {jwtAuthenticationSettings.UserIdClaimType}");
                return;
            }

            logger.LogInformation("JWT token validated successfully for user {UserId} with email {Email}",
                userId, email);

            if (context.Principal?.Identity is ClaimsIdentity claimsIdentity)
            {
                await AddCustomClaimsAsync(claimsIdentity, claims);
            }

            await base.TokenValidated(context);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during token validation");
            context.Fail("Token validation failed due to an internal error");
        }
    }

    public override async Task AuthenticationFailed(AuthenticationFailedContext context)
    {
        var errorMessage = context.Exception?.Message ?? "Unknown authentication error";

        logger.LogWarning("JWT authentication failed: {ErrorMessage}", errorMessage);

        if (logger.IsEnabled(LogLevel.Debug))
        {
            logger.LogDebug("Authentication failure details: {Exception}", context.Exception);
        }

        SetErrorResponse(context);
        await WriteErrorResponseAsync(context);
        await base.AuthenticationFailed(context);
    }

    private static void SetErrorResponse(AuthenticationFailedContext context)
    {
        context.Response.StatusCode = context.Exception switch
        {
            Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException => AuthenticationConstants.HttpStatusCodes.BadRequest,
            _ => AuthenticationConstants.HttpStatusCodes.Unauthorized
        };
        context.Response.ContentType = AuthenticationConstants.ContentTypes.ApplicationJson;
    }

    private static async Task WriteErrorResponseAsync(AuthenticationFailedContext context)
    {
        var response = new
        {
            error = AuthenticationConstants.ErrorMessages.UnauthorizedError,
            message = AuthenticationConstants.ErrorMessages.InvalidTokenMessage
        };

        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
    }

    private async Task AddCustomClaimsAsync(ClaimsIdentity claimsIdentity, IList<Claim> existingClaims)
    {
        await Task.CompletedTask;
    }
}
