using Axon.Core.Authentication.Controllers;
using Axon.Core.Authentication.Services;
using Axon.Core.Authentication.Utilities;
using CommunityToolkit.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using System;
using System.Security.Claims;
using System.Threading.Tasks;
using Xunit;

namespace Axon.Core.Authentication.Tests.Controllers;

public class AxonLoginControllerTests
{
    private readonly ITokenExchangeService tokenExchangeService = Substitute.For<ITokenExchangeService>();
    private readonly IAuthenticationService authenticationService = Substitute.For<IAuthenticationService>();
    private readonly ILogger<AxonLoginController> logger = Substitute.For<ILogger<AxonLoginController>>();
    private readonly AxonLoginController controller;

    public AxonLoginControllerTests()
    {
        controller = new AxonLoginController(tokenExchangeService, authenticationService, logger)
        {
            ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            }
        };
    }

    [Fact]
    public async Task Login_WithMissingCode_ShouldReturnBadRequest()
    {
        // Act
        var result = await controller.Login(string.Empty, "https://example.com");

        // Assert
        result.ShouldBeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.CodeRequired);
    }

    [Fact]
    public async Task Login_WithMissingRedirectUrl_ShouldReturnBadRequest()
    {
        // Act
        var result = await controller.Login("test-code", string.Empty);

        // Assert
        result.ShouldBeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.RedirectUrlRequired);
    }

    [Fact]
    public async Task Login_WithValidParameters_ShouldReturnRedirect()
    {
        // Arrange
        const string testCode = "test-code";
        const string redirectUrl = "https://example.com/dashboard";
        const string testToken = "test-token";
        var claimsPrincipal = new ClaimsPrincipal(new ClaimsIdentity("test"));

        tokenExchangeService.ExchangeCodeForTokenAsync(testCode).Returns(testToken);
        authenticationService.ValidateTokenAsync(testToken).Returns(claimsPrincipal);

        // Act
        var result = await controller.Login(testCode, redirectUrl);

        // Assert
        result.ShouldBeOfType<RedirectResult>();
        var redirectResult = (RedirectResult)result;
        redirectResult.Url.ShouldBe(redirectUrl);

        await tokenExchangeService.Received(1).ExchangeCodeForTokenAsync(testCode);
        await authenticationService.Received(1).ValidateTokenAsync(testToken);
        await authenticationService.Received(1).SignInUserAsync(Arg.Any<HttpContext>(), claimsPrincipal);
    }

    [Fact]
    public async Task Login_WithTokenValidationFailure_ShouldReturnUnauthorized()
    {
        // Arrange
        const string testCode = "test-code";
        const string redirectUrl = "https://example.com/dashboard";
        const string testToken = "test-token";

        tokenExchangeService.ExchangeCodeForTokenAsync(testCode).Returns(testToken);
        authenticationService.ValidateTokenAsync(testToken).Returns((ClaimsPrincipal)null!);

        // Act
        var result = await controller.Login(testCode, redirectUrl);

        // Assert
        result.ShouldBeOfType<UnauthorizedObjectResult>();
        var unauthorizedResult = (UnauthorizedObjectResult)result;
        unauthorizedResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.TokenValidationFailed);
    }

    [Fact]
    public async Task Login_WithNullTokenResponse_ShouldReturnUnauthorized()
    {
        // Arrange
        const string testCode = "test-code";
        const string redirectUrl = "https://example.com/dashboard";

        tokenExchangeService.ExchangeCodeForTokenAsync(testCode)
            .Returns(Task.FromException<string>(new InvalidOperationException("Failed to retrieve token from organisation API")));

        // Act
        var result = await controller.Login(testCode, redirectUrl);

        // Assert
        result.ShouldBeOfType<ObjectResult>();
        var objectResult = (ObjectResult)result;
        objectResult.StatusCode.ShouldBe(AuthenticationConstants.HttpStatusCodes.InternalServerError);
        objectResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.AuthenticationError);
    }

    [Fact]
    public async Task Login_WithException_ShouldReturnInternalServerError()
    {
        // Arrange
        const string testCode = "test-code";
        const string redirectUrl = "https://example.com/dashboard";

        tokenExchangeService.ExchangeCodeForTokenAsync(testCode)
            .Returns(Task.FromException<string>(new InvalidOperationException("Test exception")));

        // Act
        var result = await controller.Login(testCode, redirectUrl);

        // Assert
        result.ShouldBeOfType<ObjectResult>();
        var objectResult = (ObjectResult)result;
        objectResult.StatusCode.ShouldBe(AuthenticationConstants.HttpStatusCodes.InternalServerError);
        objectResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.AuthenticationError);
    }

    [Fact]
    public void Guard_Behavior_ShouldWorkCorrectly()
    {
        // Arrange & Act & Assert

        // Guard.IsNotNullOrEmpty throws exception for null/empty values
        Should.Throw<ArgumentException>(() => Guard.IsNotNullOrEmpty(null, "test"));
        Should.Throw<ArgumentException>(() => Guard.IsNotNullOrEmpty("", "test"));
        Should.NotThrow(() => Guard.IsNotNullOrEmpty("valid", "test"));

        // For conditional checks, we still use string.IsNullOrEmpty
        string.IsNullOrEmpty(null).ShouldBeTrue();
        string.IsNullOrEmpty("").ShouldBeTrue();
        string.IsNullOrEmpty("test").ShouldBeFalse();
    }
}
