using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Controllers;
using Axon.Core.Authentication.Services;
using Axon.Core.Authentication.Utilities;
using CommunityToolkit.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using Shouldly;
using System;
using System.Security.Claims;
using System.Threading.Tasks;
using Xunit;

namespace Axon.Core.Authentication.Tests.Controllers;

public class AxonLoginControllerTests
{
    private readonly ITokenService tokenService = Substitute.For<ITokenService>();
    private readonly IAuthenticationService authenticationService = Substitute.For<IAuthenticationService>();
    private readonly IOptionsMonitor<JwtAuthenticationOptions> options = Substitute.For<IOptionsMonitor<JwtAuthenticationOptions>>();
    private readonly ILogger<AxonLoginController> logger = Substitute.For<ILogger<AxonLoginController>>();
    private readonly AxonLoginController controller;
    private readonly JwtAuthenticationOptions jwtOptions;

    public AxonLoginControllerTests()
    {
        jwtOptions = new JwtAuthenticationOptions
        {
            DefaultRedirectUrl = "/dashboard",
            Issuer = "test-issuer",
            Audience = "test-audience",
            PublicKeyApiHost = "https://api.test.com"
        };

        options.CurrentValue.Returns(jwtOptions);

        controller = new AxonLoginController(tokenService, authenticationService, options, logger)
        {
            ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            }
        };
    }

    [Fact]
    public async Task Login_WithMissingCode_ShouldReturnBadRequest()
    {
        // Act
        var result = await controller.Login(string.Empty, "https://example.com");

        // Assert
        result.ShouldBeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.CodeRequired);
    }

    [Fact]
    public async Task Login_WithMissingRedirectUrl_ShouldUseDefaultRedirectUrl()
    {
        // Arrange
        const string testCode = "test-code";
        const string testToken = "test-token";
        var claimsPrincipal = new ClaimsPrincipal(new ClaimsIdentity(authenticationType: "test"));

        tokenService.GetTokenAsync(testCode).Returns(testToken);
        authenticationService.ValidateTokenAsync(testToken).Returns(claimsPrincipal);

        // Act
        var result = await controller.Login(testCode, string.Empty);

        // Assert
        result.ShouldBeOfType<RedirectResult>();
        var redirectResult = (RedirectResult)result;
        redirectResult.Url.ShouldBe(jwtOptions.DefaultRedirectUrl);

        await tokenService.Received(1).GetTokenAsync(testCode);
        await authenticationService.Received(1).ValidateTokenAsync(testToken);
        await authenticationService.Received(1).SignInUserAsync(Arg.Any<HttpContext>(), claimsPrincipal);
    }

    [Fact]
    public async Task Login_WithValidParameters_ShouldReturnRedirect()
    {
        // Arrange
        const string testCode = "test-code";
        const string redirectUrl = "https://example.com/dashboard";
        const string testToken = "test-token";
        var claimsPrincipal = new ClaimsPrincipal(new ClaimsIdentity(authenticationType: "test"));

        tokenService.GetTokenAsync(testCode).Returns(testToken);
        authenticationService.ValidateTokenAsync(testToken).Returns(claimsPrincipal);

        // Act
        var result = await controller.Login(testCode, redirectUrl);

        // Assert
        result.ShouldBeOfType<RedirectResult>();
        var redirectResult = (RedirectResult)result;
        redirectResult.Url.ShouldBe(redirectUrl);

        await tokenService.Received(1).GetTokenAsync(testCode);
        await authenticationService.Received(1).ValidateTokenAsync(testToken);
        await authenticationService.Received(1).SignInUserAsync(Arg.Any<HttpContext>(), claimsPrincipal);
    }

    [Fact]
    public async Task Login_WithTokenValidationFailure_ShouldReturnUnauthorized()
    {
        // Arrange
        const string testCode = "test-code";
        const string redirectUrl = "https://example.com/dashboard";
        const string testToken = "test-token";

        tokenService.GetTokenAsync(testCode).Returns(testToken);
        authenticationService.ValidateTokenAsync(testToken).Returns((ClaimsPrincipal)null!);

        // Act
        var result = await controller.Login(testCode, redirectUrl);

        // Assert
        result.ShouldBeOfType<UnauthorizedObjectResult>();
        var unauthorizedResult = (UnauthorizedObjectResult)result;
        unauthorizedResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.TokenValidationFailed);
    }

    [Fact]
    public async Task Login_WithEmptyTokenResponse_ShouldReturnUnauthorized()
    {
        // Arrange
        const string testCode = "test-code";
        const string redirectUrl = "https://example.com/dashboard";

        tokenService.GetTokenAsync(testCode).Returns(string.Empty);

        // Act
        var result = await controller.Login(testCode, redirectUrl);

        // Assert
        result.ShouldBeOfType<UnauthorizedObjectResult>();
        var unauthorizedResult = (UnauthorizedObjectResult)result;
        unauthorizedResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.TokenRetrievalFailed);
    }

    [Fact]
    public async Task Login_WithTokenServiceException_ShouldReturnInternalServerError()
    {
        // Arrange
        const string testCode = "test-code";
        const string redirectUrl = "https://example.com/dashboard";

        tokenService.GetTokenAsync(testCode)
            .Returns(Task.FromException<string>(new InvalidOperationException("Test exception")));

        // Act
        var result = await controller.Login(testCode, redirectUrl);

        // Assert
        result.ShouldBeOfType<ObjectResult>();
        var objectResult = (ObjectResult)result;
        objectResult.StatusCode.ShouldBe(AuthenticationConstants.HttpStatusCodes.InternalServerError);
        objectResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.AuthenticationError);
    }

    [Fact]
    public async Task Login_WithUnauthenticatedClaimsPrincipal_ShouldReturnUnauthorized()
    {
        // Arrange
        const string testCode = "test-code";
        const string redirectUrl = "https://example.com/dashboard";
        const string testToken = "test-token";
        var unauthenticatedPrincipal = new ClaimsPrincipal(new ClaimsIdentity()); // Not authenticated

        tokenService.GetTokenAsync(testCode).Returns(testToken);
        authenticationService.ValidateTokenAsync(testToken).Returns(unauthenticatedPrincipal);

        // Act
        var result = await controller.Login(testCode, redirectUrl);

        // Assert
        result.ShouldBeOfType<UnauthorizedObjectResult>();
        var unauthorizedResult = (UnauthorizedObjectResult)result;
        unauthorizedResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.TokenValidationFailed);
    }
}
