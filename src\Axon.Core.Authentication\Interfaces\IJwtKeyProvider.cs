﻿using Microsoft.IdentityModel.Tokens;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Axon.Core.Authentication.Interfaces;

public interface IJwtKeyProvider
{
    /// <summary>
    /// Asynchronously retrieves JWT keys for the specified key identifier.
    /// </summary>
    /// <param name="kid">The key identifier</param>
    /// <returns>A task containing the list of JSON Web Keys</returns>
    Task<IList<JsonWebKey>> Get<PERSON>eyAsync(string kid);
}
