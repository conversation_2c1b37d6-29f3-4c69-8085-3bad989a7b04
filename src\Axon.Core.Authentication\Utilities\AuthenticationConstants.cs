namespace Axon.Core.Authentication.Utilities;

public sealed class AuthenticationConstants
{
    private AuthenticationConstants() { }

    public static class ErrorMessages
    {
        public const string CodeRequired = "Code parameter is required";
        public const string RedirectUrlRequired = "RedirectUrl parameter is required";
        public const string TokenRetrievalFailed = "Failed to retrieve authentication token";
        public const string TokenValidationFailed = "Invalid authentication token";
        public const string AuthenticationError = "An error occurred during authentication";
        public const string UnauthorizedError = "unauthorized";
        public const string InvalidTokenMessage = "Invalid or expired token";
    }

    public static class ContentTypes
    {
        public const string ApplicationJson = "application/json";
    }

    public static class HttpStatusCodes
    {
        public const int BadRequest = 400;
        public const int Unauthorized = 401;
        public const int InternalServerError = 500;
    }
}
